"use client";

import React, { useEffect, useRef, useState } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  Fade,
  Grid,
  Card,
  CardContent,
  Avatar,
  Rating,
  Button,
  Chip,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  BsStar,
  BsStarFill,
  BsGoogle,
  BsChevronLeft,
  BsChevronRight,
  BsQuote,
  BsShieldCheck,
  BsCalendar3,
} from "react-icons/bs";

// Mock reviews data - replace with real data from your review service
const mockReviews = [
  {
    id: 1,
    name: "<PERSON>",
    avatar:
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    rating: 5,
    date: "vor 2 Wochen",
    text: "Absolut fantastische Atmosphäre! Die Dachterrasse bietet einen atemberaubenden Ausblick und der Service war erstklassig. Perfekt für einen besonderen Abend.",
    platform: "Google",
    verified: true,
  },
  {
    id: 2,
    name: "<PERSON>",
    avatar:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    rating: 5,
    date: "vor 1 Woche",
    text: "Vibes Rooftop ist definitiv ein Highlight! Tolle Shisha-Auswahl, entspannte Musik und das Personal ist super freundlich. Komme gerne wieder!",
    platform: "Google",
    verified: true,
  },
  {
    id: 3,
    name: "Lisa R.",
    avatar:
      "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    rating: 5,
    date: "vor 3 Tagen",
    text: "Haben hier unseren Geburtstag gefeiert - einfach perfekt! Die Location ist wunderschön und das Team hat alles möglich gemacht. Sehr empfehlenswert!",
    platform: "Google",
    verified: true,
  },
  {
    id: 4,
    name: "Thomas B.",
    avatar:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    rating: 5,
    date: "vor 5 Tagen",
    text: "Moderne Einrichtung, entspannte Atmosphäre und faire Preise. Die Cocktails sind auch top! Definitiv einer der besten Spots in der Gegend.",
    platform: "Google",
    verified: true,
  },
  {
    id: 5,
    name: "Anna S.",
    avatar:
      "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
    rating: 5,
    date: "vor 1 Woche",
    text: "Wunderschöne Location mit tollem Ambiente. Perfekt für einen romantischen Abend oder um mit Freunden zu entspannen. Service war ausgezeichnet!",
    platform: "Google",
    verified: true,
  },
  {
    id: 6,
    name: "David L.",
    avatar:
      "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
    rating: 5,
    date: "vor 4 Tagen",
    text: "Beste Shisha Bar weit und breit! Qualität stimmt, Preise sind fair und die Aussicht ist einfach unschlagbar. Absolute Empfehlung!",
    platform: "Google",
    verified: true,
  },
];

const ReviewWidget = () => {
  const [currentReviewIndex, setCurrentReviewIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const widgetRef = useRef(null);

  // Auto-scroll reviews
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentReviewIndex((prev) => (prev + 1) % mockReviews.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const nextReview = () => {
    setCurrentReviewIndex((prev) => (prev + 1) % mockReviews.length);
    setIsAutoPlaying(false);
  };

  const prevReview = () => {
    setCurrentReviewIndex(
      (prev) => (prev - 1 + mockReviews.length) % mockReviews.length
    );
    setIsAutoPlaying(false);
  };

  const averageRating =
    mockReviews.reduce((acc, review) => acc + review.rating, 0) /
    mockReviews.length;
  const totalReviews = mockReviews.length;

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        position: "relative",
        overflow: "hidden",
        background: `
          radial-gradient(circle at 30% 70%, rgba(103, 247, 86, 0.08) 0%, transparent 60%),
          radial-gradient(circle at 70% 30%, rgba(74, 29, 31, 0.12) 0%, transparent 60%),
          linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, transparent 50%)
        `,
      }}
    >
      {/* Animated background elements */}
      <Box
        sx={{
          position: "absolute",
          top: "10%",
          left: "5%",
          width: "200px",
          height: "200px",
          background:
            "radial-gradient(circle, rgba(103, 247, 86, 0.06) 0%, transparent 70%)",
          borderRadius: "50%",
          animation: "float 6s ease-in-out infinite",
          "@keyframes float": {
            "0%, 100%": { transform: "translateY(0px) rotate(0deg)" },
            "50%": { transform: "translateY(-20px) rotate(180deg)" },
          },
        }}
      />
      <Box
        sx={{
          position: "absolute",
          top: "60%",
          right: "10%",
          width: "150px",
          height: "150px",
          background:
            "radial-gradient(circle, rgba(74, 29, 31, 0.08) 0%, transparent 70%)",
          borderRadius: "50%",
          animation: "float 8s ease-in-out infinite reverse",
        }}
      />

      <Container sx={{ position: "relative", zIndex: 1 }}>
        {/* Section Header */}
        <Box sx={{ textAlign: "center", mb: { xs: 6, md: 8 } }}>
          <Fade in timeout={800}>
            <Box>
              {/* Rating Overview */}
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  gap: 2,
                  mb: 4,
                  flexWrap: "wrap",
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <BsGoogle style={{ color: "#4285f4", fontSize: "24px" }} />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Google Reviews
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Rating
                    value={averageRating}
                    readOnly
                    precision={0.1}
                    sx={{
                      "& .MuiRating-iconFilled": {
                        color: "#67f756",
                      },
                    }}
                  />
                  <Typography
                    variant="h6"
                    sx={{ fontWeight: 700, color: "primary.main" }}
                  >
                    {averageRating.toFixed(1)}
                  </Typography>
                  <Typography variant="body2" sx={{ color: "text.secondary" }}>
                    ({totalReviews} Bewertungen)
                  </Typography>
                </Box>
              </Box>

              <Typography
                variant="h1"
                component="h2"
                sx={{
                  fontSize: { xs: 32, md: 48 },
                  fontWeight: 800,
                  mb: 3,
                  background:
                    "linear-gradient(135deg, #ffffff 0%, #67f756 100%)",
                  backgroundClip: "text",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  textAlign: "center",
                  letterSpacing: "-0.02em",
                }}
              >
                Was unsere Gäste sagen
              </Typography>

              <Typography
                variant="body1"
                sx={{
                  color: "text.secondary",
                  fontSize: { xs: 18, md: 20 },
                  maxWidth: "700px",
                  mx: "auto",
                  lineHeight: 1.6,
                  fontWeight: 400,
                }}
              >
                Erleben Sie, warum Vibes Rooftop der perfekte Ort für
                unvergessliche Momente ist
              </Typography>
            </Box>
          </Fade>
        </Box>

        {/* Featured Review Carousel */}
        <Fade in timeout={1200}>
          <Box sx={{ mb: { xs: 6, md: 8 } }}>
            <Paper
              elevation={0}
              sx={{
                p: { xs: 4, md: 6 },
                background: `
                  linear-gradient(135deg,
                    rgba(255, 255, 255, 0.12) 0%,
                    rgba(255, 255, 255, 0.08) 50%,
                    rgba(103, 247, 86, 0.1) 100%
                  )
                `,
                backdropFilter: "blur(20px)",
                border: "2px solid rgba(103, 247, 86, 0.2)",
                borderRadius: "32px",
                position: "relative",
                overflow: "hidden",
                transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                boxShadow: "0 8px 32px rgba(103, 247, 86, 0.1)",
                "&:hover": {
                  transform: "translateY(-6px)",
                  boxShadow: "0 24px 48px rgba(103, 247, 86, 0.2)",
                  border: "2px solid rgba(103, 247, 86, 0.4)",
                },
              }}
            >
              {/* Navigation Controls */}
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: 4,
                }}
              >
                <Typography
                  variant="h5"
                  sx={{ fontWeight: 700, color: "primary.main" }}
                >
                  Aktuelle Bewertungen
                </Typography>
                <Box sx={{ display: "flex", gap: 1 }}>
                  <IconButton
                    onClick={prevReview}
                    sx={{
                      bgcolor: "rgba(103, 247, 86, 0.1)",
                      color: "primary.main",
                      "&:hover": { bgcolor: "rgba(103, 247, 86, 0.2)" },
                    }}
                  >
                    <BsChevronLeft />
                  </IconButton>
                  <IconButton
                    onClick={nextReview}
                    sx={{
                      bgcolor: "rgba(103, 247, 86, 0.1)",
                      color: "primary.main",
                      "&:hover": { bgcolor: "rgba(103, 247, 86, 0.2)" },
                    }}
                  >
                    <BsChevronRight />
                  </IconButton>
                </Box>
              </Box>

              {/* Current Review */}
              <Box sx={{ position: "relative", minHeight: "200px" }}>
                {mockReviews.map((review, index) => (
                  <Fade
                    key={review.id}
                    in={index === currentReviewIndex}
                    timeout={500}
                    style={{
                      position:
                        index === currentReviewIndex ? "relative" : "absolute",
                      top: 0,
                      left: 0,
                      width: "100%",
                      display: index === currentReviewIndex ? "block" : "none",
                    }}
                  >
                    <Box>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "flex-start",
                          gap: 3,
                          mb: 3,
                        }}
                      >
                        <Avatar
                          src={review.avatar}
                          sx={{
                            width: 64,
                            height: 64,
                            border: "3px solid rgba(103, 247, 86, 0.3)",
                          }}
                        />
                        <Box sx={{ flex: 1 }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 2,
                              mb: 1,
                            }}
                          >
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                              {review.name}
                            </Typography>
                            {review.verified && (
                              <Tooltip title="Verifizierte Bewertung">
                                <Chip
                                  icon={
                                    <BsShieldCheck
                                      style={{ fontSize: "14px" }}
                                    />
                                  }
                                  label="Verifiziert"
                                  size="small"
                                  sx={{
                                    bgcolor: "rgba(103, 247, 86, 0.2)",
                                    color: "primary.main",
                                    fontWeight: 600,
                                    fontSize: "12px",
                                  }}
                                />
                              </Tooltip>
                            )}
                          </Box>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 2,
                              mb: 2,
                            }}
                          >
                            <Rating
                              value={review.rating}
                              readOnly
                              size="small"
                              sx={{
                                "& .MuiRating-iconFilled": {
                                  color: "#67f756",
                                },
                              }}
                            />
                            <Typography
                              variant="body2"
                              sx={{ color: "text.secondary" }}
                            >
                              {review.date}
                            </Typography>
                            <Chip
                              icon={<BsGoogle style={{ fontSize: "12px" }} />}
                              label={review.platform}
                              size="small"
                              sx={{
                                bgcolor: "rgba(66, 133, 244, 0.2)",
                                color: "#4285f4",
                                fontWeight: 600,
                                fontSize: "11px",
                              }}
                            />
                          </Box>
                        </Box>
                      </Box>

                      <Box sx={{ position: "relative", pl: { xs: 0, md: 10 } }}>
                        <BsQuote
                          style={{
                            position: "absolute",
                            top: "-10px",
                            left: { xs: "-10px", md: "30px" },
                            fontSize: "32px",
                            color: "rgba(103, 247, 86, 0.3)",
                            transform: "rotate(180deg)",
                          }}
                        />
                        <Typography
                          variant="body1"
                          sx={{
                            fontSize: { xs: 16, md: 18 },
                            lineHeight: 1.7,
                            fontStyle: "italic",
                            color: "text.primary",
                          }}
                        >
                          "{review.text}"
                        </Typography>
                      </Box>
                    </Box>
                  </Fade>
                ))}
              </Box>

              {/* Review Indicators */}
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  gap: 1,
                  mt: 4,
                }}
              >
                {mockReviews.map((_, index) => (
                  <Box
                    key={index}
                    onClick={() => {
                      setCurrentReviewIndex(index);
                      setIsAutoPlaying(false);
                    }}
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: "50%",
                      bgcolor:
                        index === currentReviewIndex
                          ? "primary.main"
                          : "rgba(255, 255, 255, 0.3)",
                      cursor: "pointer",
                      transition: "all 0.3s ease",
                      "&:hover": {
                        bgcolor:
                          index === currentReviewIndex
                            ? "primary.main"
                            : "rgba(255, 255, 255, 0.5)",
                        transform: "scale(1.2)",
                      },
                    }}
                  />
                ))}
              </Box>
            </Paper>
          </Box>
        </Fade>

        {/* Reviews Grid */}
        <Fade in timeout={1600}>
          <Box sx={{ mb: { xs: 6, md: 8 } }}>
            <Typography
              variant="h4"
              sx={{
                textAlign: "center",
                mb: 4,
                fontWeight: 700,
                color: "primary.main",
              }}
            >
              Alle Bewertungen
            </Typography>
            <Grid container spacing={3}>
              {mockReviews.slice(0, 6).map((review) => (
                <Grid item xs={12} md={6} lg={4} key={review.id}>
                  <Card
                    elevation={0}
                    sx={{
                      height: "100%",
                      background: `
                        linear-gradient(135deg,
                          rgba(255, 255, 255, 0.08) 0%,
                          rgba(255, 255, 255, 0.04) 100%
                        )
                      `,
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(103, 247, 86, 0.1)",
                      borderRadius: "20px",
                      transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                      "&:hover": {
                        transform: "translateY(-8px)",
                        boxShadow: "0 16px 32px rgba(103, 247, 86, 0.15)",
                        border: "1px solid rgba(103, 247, 86, 0.3)",
                      },
                    }}
                  >
                    <CardContent sx={{ p: 3 }}>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 2,
                          mb: 2,
                        }}
                      >
                        <Avatar
                          src={review.avatar}
                          sx={{
                            width: 48,
                            height: 48,
                            border: "2px solid rgba(103, 247, 86, 0.2)",
                          }}
                        />
                        <Box sx={{ flex: 1 }}>
                          <Typography
                            variant="subtitle1"
                            sx={{ fontWeight: 600, mb: 0.5 }}
                          >
                            {review.name}
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 1,
                            }}
                          >
                            <Rating
                              value={review.rating}
                              readOnly
                              size="small"
                              sx={{
                                "& .MuiRating-iconFilled": {
                                  color: "#67f756",
                                },
                              }}
                            />
                            <Typography
                              variant="caption"
                              sx={{ color: "text.secondary" }}
                            >
                              {review.date}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>

                      <Typography
                        variant="body2"
                        sx={{
                          lineHeight: 1.6,
                          color: "text.secondary",
                          display: "-webkit-box",
                          WebkitLineClamp: 4,
                          WebkitBoxOrient: "vertical",
                          overflow: "hidden",
                        }}
                      >
                        {review.text}
                      </Typography>

                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          mt: 2,
                        }}
                      >
                        <Chip
                          icon={<BsGoogle style={{ fontSize: "12px" }} />}
                          label={review.platform}
                          size="small"
                          sx={{
                            bgcolor: "rgba(66, 133, 244, 0.2)",
                            color: "#4285f4",
                            fontWeight: 600,
                            fontSize: "11px",
                          }}
                        />
                        {review.verified && (
                          <Chip
                            icon={
                              <BsShieldCheck style={{ fontSize: "12px" }} />
                            }
                            label="Verifiziert"
                            size="small"
                            sx={{
                              bgcolor: "rgba(103, 247, 86, 0.2)",
                              color: "primary.main",
                              fontWeight: 600,
                              fontSize: "11px",
                            }}
                          />
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Fade>

        {/* Call to Action */}
        <Fade in timeout={2000}>
          <Box sx={{ textAlign: "center" }}>
            <Paper
              elevation={0}
              sx={{
                p: { xs: 4, md: 6 },
                background: `
                  linear-gradient(135deg,
                    rgba(103, 247, 86, 0.1) 0%,
                    rgba(103, 247, 86, 0.05) 100%
                  )
                `,
                backdropFilter: "blur(20px)",
                border: "2px solid rgba(103, 247, 86, 0.2)",
                borderRadius: "24px",
                position: "relative",
                overflow: "hidden",
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 700,
                  mb: 2,
                  color: "primary.main",
                }}
              >
                Werden Sie Teil unserer zufriedenen Gäste
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: "text.secondary",
                  fontSize: { xs: 16, md: 18 },
                  mb: 4,
                  maxWidth: "600px",
                  mx: "auto",
                  lineHeight: 1.6,
                }}
              >
                Buchen Sie jetzt Ihren unvergesslichen Abend bei Vibes Rooftop
                und erleben Sie selbst, warum unsere Gäste uns so lieben.
              </Typography>
              <Button
                variant="contained"
                size="large"
                sx={{
                  px: 4,
                  py: 1.5,
                  fontSize: 18,
                  fontWeight: 700,
                  borderRadius: "50px",
                  textTransform: "none",
                  boxShadow: "0 8px 24px rgba(103, 247, 86, 0.3)",
                  "&:hover": {
                    transform: "translateY(-2px)",
                    boxShadow: "0 12px 32px rgba(103, 247, 86, 0.4)",
                  },
                }}
              >
                Jetzt reservieren
              </Button>
            </Paper>
          </Box>
        </Fade>

        {/* Free Review Widget Integration Info */}
        <Fade in timeout={2400}>
          <Box
            sx={{
              mt: { xs: 6, md: 8 },
              textAlign: "center",
              p: 3,
              background: "rgba(255, 255, 255, 0.02)",
              borderRadius: "16px",
              border: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            <Typography
              variant="h6"
              sx={{ color: "primary.main", mb: 2, fontWeight: 600 }}
            >
              💡 Tipp für Website-Betreiber
            </Typography>
            <Typography variant="body2" sx={{ color: "text.secondary", mb: 2 }}>
              Möchten Sie echte Google Reviews auf Ihrer Website anzeigen?
              Nutzen Sie kostenlose Services wie:
            </Typography>
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                gap: 2,
                flexWrap: "wrap",
              }}
            >
              <Chip
                label="Shapo.io (kostenlos)"
                variant="outlined"
                sx={{ color: "primary.main", borderColor: "primary.main" }}
              />
              <Chip
                label="EmbedSocial"
                variant="outlined"
                sx={{ color: "primary.main", borderColor: "primary.main" }}
              />
              <Chip
                label="SociableKit"
                variant="outlined"
                sx={{ color: "primary.main", borderColor: "primary.main" }}
              />
            </Box>
          </Box>
        </Fade>
      </Container>
    </Box>
  );
};

export default ReviewWidget;
