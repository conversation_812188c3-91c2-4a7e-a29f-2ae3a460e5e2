"use client";

import React, { useEffect, useRef } from "react";
import { Box, Container, Typography, Paper, Fade } from "@mui/material";
import { BsStar, BsStarFill } from "react-icons/bs";

const ReviewWidget = () => {
  const widgetRef = useRef(null);

  useEffect(() => {
    // Load the review widget script
    const script = document.createElement("script");
    script.src = "https://widget.review-widget.net/js/widget.js";
    script.async = true;
    document.head.appendChild(script);

    // Add custom CSS to make the widget larger
    const style = document.createElement("style");
    style.textContent = `
      .review-widget_net {
        min-height: 500px !important;
        width: 100% !important;
      }
      .review-widget_net iframe {
        min-height: 500px !important;
        width: 100% !important;
        border-radius: 20px !important;
      }
      .review-widget_net > div {
        min-height: 500px !important;
        width: 100% !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      // Cleanup script and style on unmount
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        position: "relative",
        overflow: "hidden",
        minHeight: { xs: "600px", md: "700px" },
        background: `
          radial-gradient(circle at 30% 70%, rgba(103, 247, 86, 0.12) 0%, transparent 60%),
          radial-gradient(circle at 70% 30%, rgba(74, 29, 31, 0.15) 0%, transparent 60%),
          linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, transparent 50%)
        `,
      }}
    >
      {/* Modern geometric background pattern */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background: `
            linear-gradient(45deg, transparent 48%, rgba(103, 247, 86, 0.03) 49%, rgba(103, 247, 86, 0.03) 51%, transparent 52%),
            linear-gradient(-45deg, transparent 48%, rgba(103, 247, 86, 0.03) 49%, rgba(103, 247, 86, 0.03) 51%, transparent 52%)
          `,
          backgroundSize: "80px 80px",
          opacity: 0.5,
          zIndex: 0,
        }}
      />

      <Container sx={{ position: "relative", zIndex: 1 }}>
        {/* Section Header */}
        <Box sx={{ textAlign: "center", mb: { xs: 6, md: 8 } }}>
          <Fade in timeout={800}>
            <Box>
              <Box sx={{ display: "flex", justifyContent: "center", mb: 3 }}>
                {[...Array(5)].map((_, index) => (
                  <BsStarFill
                    key={index}
                    style={{
                      color: "#67f756",
                      fontSize: "32px",
                      margin: "0 4px",
                      filter: "drop-shadow(0 0 12px rgba(103, 247, 86, 0.6))",
                    }}
                  />
                ))}
              </Box>

              <Typography
                variant="h1"
                component="h2"
                sx={{
                  fontSize: { xs: 32, md: 48 },
                  fontWeight: 800,
                  mb: 3,
                  background:
                    "linear-gradient(135deg, #ffffff 0%, #67f756 100%)",
                  backgroundClip: "text",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  textAlign: "center",
                  letterSpacing: "-0.02em",
                }}
              >
                Was unsere Gäste sagen
              </Typography>

              <Typography
                variant="body1"
                sx={{
                  color: "text.secondary",
                  fontSize: { xs: 18, md: 20 },
                  maxWidth: "700px",
                  mx: "auto",
                  lineHeight: 1.6,
                  fontWeight: 400,
                }}
              >
                Erleben Sie, warum Vibes Rooftop der perfekte Ort für
                unvergessliche Momente ist
              </Typography>
            </Box>
          </Fade>
        </Box>

        {/* Modern Review Widget Container */}
        <Fade in timeout={1200}>
          <Paper
            elevation={0}
            sx={{
              p: { xs: 4, md: 6 },
              minHeight: { xs: "500px", md: "600px" },
              background: `
                linear-gradient(135deg,
                  rgba(255, 255, 255, 0.12) 0%,
                  rgba(255, 255, 255, 0.08) 50%,
                  rgba(103, 247, 86, 0.1) 100%
                )
              `,
              backdropFilter: "blur(20px)",
              border: "2px solid rgba(103, 247, 86, 0.2)",
              borderRadius: "32px",
              position: "relative",
              overflow: "hidden",
              transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
              boxShadow: "0 8px 32px rgba(103, 247, 86, 0.1)",
              "&:hover": {
                transform: "translateY(-6px)",
                boxShadow: "0 24px 48px rgba(103, 247, 86, 0.2)",
                border: "2px solid rgba(103, 247, 86, 0.4)",
              },
            }}
          >
            {/* Subtle glow effect */}
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                background:
                  "radial-gradient(circle at 50% 0%, rgba(103, 247, 86, 0.1) 0%, transparent 70%)",
                pointerEvents: "none",
                zIndex: 0,
              }}
            />

            {/* Review Widget */}
            <Box
              ref={widgetRef}
              sx={{
                position: "relative",
                zIndex: 1,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                minHeight: { xs: "450px", md: "550px" },
                "& .review-widget_net": {
                  width: "100%",
                  minHeight: { xs: "450px", md: "550px" },
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                },
                // Custom styling for the widget content
                "& iframe": {
                  borderRadius: "20px",
                  border: "none",
                  width: "100% !important",
                  minHeight: { xs: "450px", md: "550px" },
                },
                // Style the widget container
                "& > div": {
                  width: "100%",
                  minHeight: { xs: "450px", md: "550px" },
                },
              }}
            >
              <div
                className="review-widget_net"
                data-uuid="9d33039b-9d4f-4a17-be3f-1a4b0a4950dc"
                data-template="1"
                data-lang="de"
                data-theme="dark"
                data-width="100%"
                data-height="500"
                style={{
                  width: "100%",
                  minHeight: "500px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              />
            </Box>
          </Paper>
        </Fade>

        {/* Call to Action */}
        <Fade in timeout={1600}>
          <Box sx={{ textAlign: "center", mt: { xs: 6, md: 8 } }}>
            <Typography
              variant="h6"
              sx={{
                color: "primary.main",
                fontSize: { xs: 16, md: 18 },
                fontWeight: 600,
                mb: 2,
              }}
            >
              Werden Sie Teil unserer zufriedenen Gäste
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "text.secondary",
                fontSize: 14,
                opacity: 0.8,
              }}
            >
              Buchen Sie jetzt Ihren unvergesslichen Abend bei Vibes Rooftop
            </Typography>
          </Box>
        </Fade>
      </Container>
    </Box>
  );
};

export default ReviewWidget;
