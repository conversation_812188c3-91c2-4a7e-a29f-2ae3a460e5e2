"use client";

import React, { useEffect, useRef } from "react";
import { Box, Container, Typography, Paper, Fade } from "@mui/material";
import { BsGoogle, BsStarFill } from "react-icons/bs";

/**
 * Shapo.io Integration Component
 * 
 * This component integrates with Shapo.io's free Google Reviews widget.
 * To use this component:
 * 
 * 1. Sign up at https://shapo.io (free account)
 * 2. Import your Google Business reviews
 * 3. Create a widget and get your widget ID
 * 4. Replace 'YOUR_WIDGET_ID' below with your actual widget ID
 * 5. Uncomment this component in app/page.js and comment out the mock ReviewWidget
 */

const ShapoReviewWidget = ({ widgetId = "YOUR_WIDGET_ID" }) => {
  const widgetRef = useRef(null);

  useEffect(() => {
    // Only load if we have a real widget ID
    if (widgetId === "YOUR_WIDGET_ID") {
      console.warn("Please replace 'YOUR_WIDGET_ID' with your actual Shapo widget ID");
      return;
    }

    // Load Shapo widget script
    const script = document.createElement("script");
    script.src = "https://widget.shapo.io/widget.js";
    script.async = true;
    script.onload = () => {
      // Initialize widget after script loads
      if (window.ShapoWidget && widgetRef.current) {
        window.ShapoWidget.init();
      }
    };
    document.head.appendChild(script);

    // Custom CSS for better integration
    const style = document.createElement("style");
    style.textContent = `
      .shapo-widget {
        border-radius: 20px !important;
        background: transparent !important;
      }
      .shapo-widget iframe {
        border-radius: 20px !important;
        border: none !important;
      }
      /* Dark theme adjustments */
      .shapo-widget .review-card {
        background: rgba(255, 255, 255, 0.08) !important;
        border: 1px solid rgba(103, 247, 86, 0.1) !important;
        border-radius: 16px !important;
      }
      .shapo-widget .review-text {
        color: #C6C6C6 !important;
      }
      .shapo-widget .reviewer-name {
        color: #ffffff !important;
      }
      .shapo-widget .star-rating {
        color: #67f756 !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      // Cleanup
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, [widgetId]);

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        position: "relative",
        overflow: "hidden",
        background: `
          radial-gradient(circle at 30% 70%, rgba(103, 247, 86, 0.08) 0%, transparent 60%),
          radial-gradient(circle at 70% 30%, rgba(74, 29, 31, 0.12) 0%, transparent 60%),
          linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, transparent 50%)
        `,
      }}
    >
      {/* Animated background elements */}
      <Box
        sx={{
          position: "absolute",
          top: "10%",
          left: "5%",
          width: "200px",
          height: "200px",
          background: "radial-gradient(circle, rgba(103, 247, 86, 0.06) 0%, transparent 70%)",
          borderRadius: "50%",
          animation: "float 6s ease-in-out infinite",
          "@keyframes float": {
            "0%, 100%": { transform: "translateY(0px) rotate(0deg)" },
            "50%": { transform: "translateY(-20px) rotate(180deg)" },
          },
        }}
      />

      <Container sx={{ position: "relative", zIndex: 1 }}>
        {/* Section Header */}
        <Box sx={{ textAlign: "center", mb: { xs: 6, md: 8 } }}>
          <Fade in timeout={800}>
            <Box>
              {/* Google Reviews Badge */}
              <Box sx={{ 
                display: "flex", 
                justifyContent: "center", 
                alignItems: "center",
                gap: 2,
                mb: 4,
                flexWrap: "wrap"
              }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <BsGoogle style={{ color: "#4285f4", fontSize: "24px" }} />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Google Reviews
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  {[...Array(5)].map((_, index) => (
                    <BsStarFill
                      key={index}
                      style={{
                        color: "#67f756",
                        fontSize: "20px",
                        filter: "drop-shadow(0 0 8px rgba(103, 247, 86, 0.4))",
                      }}
                    />
                  ))}
                </Box>
              </Box>

              <Typography
                variant="h1"
                component="h2"
                sx={{
                  fontSize: { xs: 32, md: 48 },
                  fontWeight: 800,
                  mb: 3,
                  background: "linear-gradient(135deg, #ffffff 0%, #67f756 100%)",
                  backgroundClip: "text",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  textAlign: "center",
                  letterSpacing: "-0.02em",
                }}
              >
                Was unsere Gäste sagen
              </Typography>

              <Typography
                variant="body1"
                sx={{
                  color: "text.secondary",
                  fontSize: { xs: 18, md: 20 },
                  maxWidth: "700px",
                  mx: "auto",
                  lineHeight: 1.6,
                  fontWeight: 400,
                }}
              >
                Echte Bewertungen von echten Gästen - direkt von Google
              </Typography>
            </Box>
          </Fade>
        </Box>

        {/* Shapo Widget Container */}
        <Fade in timeout={1200}>
          <Paper
            elevation={0}
            sx={{
              p: { xs: 4, md: 6 },
              minHeight: { xs: "500px", md: "600px" },
              background: `
                linear-gradient(135deg,
                  rgba(255, 255, 255, 0.12) 0%,
                  rgba(255, 255, 255, 0.08) 50%,
                  rgba(103, 247, 86, 0.1) 100%
                )
              `,
              backdropFilter: "blur(20px)",
              border: "2px solid rgba(103, 247, 86, 0.2)",
              borderRadius: "32px",
              position: "relative",
              overflow: "hidden",
              transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
              boxShadow: "0 8px 32px rgba(103, 247, 86, 0.1)",
              "&:hover": {
                transform: "translateY(-6px)",
                boxShadow: "0 24px 48px rgba(103, 247, 86, 0.2)",
                border: "2px solid rgba(103, 247, 86, 0.4)",
              },
            }}
          >
            {/* Shapo Widget */}
            <Box
              ref={widgetRef}
              sx={{
                position: "relative",
                zIndex: 1,
                minHeight: { xs: "450px", md: "550px" },
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {widgetId === "YOUR_WIDGET_ID" ? (
                // Placeholder when no widget ID is provided
                <Box sx={{ textAlign: "center", py: 8 }}>
                  <Typography variant="h5" sx={{ color: "primary.main", mb: 2 }}>
                    🚀 Bereit für echte Reviews?
                  </Typography>
                  <Typography variant="body1" sx={{ color: "text.secondary", mb: 3 }}>
                    Ersetzen Sie 'YOUR_WIDGET_ID' mit Ihrer echten Shapo Widget ID
                  </Typography>
                  <Typography variant="body2" sx={{ color: "text.secondary" }}>
                    1. Kostenloses Konto bei shapo.io erstellen<br/>
                    2. Google Reviews importieren<br/>
                    3. Widget ID hier einfügen
                  </Typography>
                </Box>
              ) : (
                // Actual Shapo widget
                <div 
                  className="shapo-widget" 
                  data-widget-id={widgetId}
                  style={{
                    width: "100%",
                    minHeight: "500px",
                  }}
                />
              )}
            </Box>
          </Paper>
        </Fade>

        {/* Integration Instructions */}
        {widgetId === "YOUR_WIDGET_ID" && (
          <Fade in timeout={1600}>
            <Box sx={{ 
              mt: { xs: 4, md: 6 }, 
              textAlign: "center",
              p: 3,
              background: "rgba(255, 255, 255, 0.02)",
              borderRadius: "16px",
              border: "1px solid rgba(255, 255, 255, 0.1)"
            }}>
              <Typography variant="h6" sx={{ color: "primary.main", mb: 2, fontWeight: 600 }}>
                💡 So aktivieren Sie echte Google Reviews
              </Typography>
              <Typography variant="body2" sx={{ color: "text.secondary", mb: 2 }}>
                Shapo.io bietet 10 kostenlose Google Reviews - perfekt für kleine Unternehmen!
              </Typography>
              <Box sx={{ 
                display: "flex", 
                justifyContent: "center", 
                gap: 2, 
                flexWrap: "wrap",
                "& > *": { 
                  bgcolor: "rgba(103, 247, 86, 0.1)",
                  color: "primary.main",
                  px: 2,
                  py: 1,
                  borderRadius: "8px",
                  fontSize: "14px"
                }
              }}>
                <Box>✅ Kostenlos</Box>
                <Box>✅ Keine Kreditkarte</Box>
                <Box>✅ 10 Reviews</Box>
                <Box>✅ Anpassbar</Box>
              </Box>
            </Box>
          </Fade>
        )}
      </Container>
    </Box>
  );
};

export default ShapoReviewWidget;
